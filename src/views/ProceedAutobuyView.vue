<script setup lang="ts">
/**
 * so we create a new route /proceed-autobuy with the following query parameters:
 *
 * account_id - id of the account to be refilled
 * type - card type
 * system - payment system
 * code - promo code
 * start_balance - start balance of the card
 *
 * {
 *   "account_id": "70913",
 *   "type": "ultima-3ds",
 *   "system": "5",
 *   "code": "333",
 *   "start_balance": "420"
 * }
 *
 * http://localhost:5173/app/proceed-autobuy?account_id=1&type=ultima&system=qiwi&code=123456&start_balance=10000
 *
 *
 */
import { useRoute, type LocationQueryValue } from "vue-router";
import { computed } from "vue";

const route = useRoute();

type AutobuyQueryParams = {
  account_id?: string;
  type?: string;
  system?: string;
  code?: string;
  start_balance?: string;
};

// Helper function to safely extract string from query parameter
// Vue Router's LocationQueryValue can be string | null
const getQueryParam = (
  param: LocationQueryValue | LocationQueryValue[],
  defaultValue?: string
): string => {
  if (Array.isArray(param)) {
    // If it's an array, take the first non-null value
    return param.find((p) => p !== null) || defaultValue || "";
  }
  return param || defaultValue || "";
};

const queryParams = computed<AutobuyQueryParams>(() => {
  return {
    account_id: getQueryParam(route.query.account_id, "1"),
    type: getQueryParam(route.query.type, "ultima-3ds"),
    system: getQueryParam(route.query.system, "5"),
    code: getQueryParam(route.query.code, "333"),
    start_balance: getQueryParam(route.query.start_balance, "420"),
  };
});
</script>

<template>
  <div class="proceed-autobuy-view">
    <h1>Proceed Autobuy View</h1>
    <pre>{{ queryParams }}</pre>
  </div>
</template>

<style scoped lang="scss"></style>
